"""
Service tạo file DOCX cho đề thi thông minh theo chuẩn THPT 2025
"""

import logging
import os
import tempfile
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List, Union

from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_PARAGRAPH_ALIGNMENT
from docx.enum.table import WD_TABLE_ALIGNMENT
from docx.oxml.shared import OxmlElement, qn

from app.models.smart_exam_models import SmartExamRequest

logger = logging.getLogger(__name__)


class SmartExamDocxService:
    """Service tạo file DOCX cho đề thi thông minh theo chuẩn THPT 2025"""

    def __init__(self):
        self.temp_dir = Path(tempfile.gettempdir()) / "smart_exams"
        self.temp_dir.mkdir(exist_ok=True)

    async def create_smart_exam_docx(
        self, exam_data: Dict[str, Any], exam_request: Union[SmartExamRequest, Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        Tạo file DOCX cho đề thi thông minh theo chuẩn THPT 2025

        Args:
            exam_data: Dữ liệu đề thi đã được tạo
            exam_request: Request gốc chứa thông tin đề thi

        Returns:
            Dict chứa thông tin file đã tạo
        """
        try:
            # Tạo document mới
            doc = Document()

            # Thiết lập style
            self._setup_document_style(doc)

            # Tạo trang bìa
            self._create_cover_page(doc, exam_request, exam_data)

            # Tạo thông tin đề thi
            self._create_exam_info(doc, exam_request, exam_data)

            # Tạo bảng hóa trị cho môn Hóa học
            subject = self._get_field(exam_request, "subject", "")
            if "hóa" in subject.lower():
                self._create_chemistry_valence_table(doc, exam_data.get("questions", []))

            # Tạo nội dung đề thi theo 3 phần
            self._create_exam_content_by_parts(doc, exam_data.get("questions", []))

            # Thêm chữ "Hết"
            self._add_exam_ending(doc)

            # Tạo đáp án theo chuẩn THPT 2025
            self._create_thpt_2025_answer_section(doc, exam_data.get("questions", []))

            # Lưu file
            filename = self._generate_filename(exam_request)
            filepath = self.temp_dir / filename
            doc.save(str(filepath))

            logger.info(f"Created smart exam DOCX: {filepath}")
            return {
                "success": True,
                "file_path": str(filepath),
                "filename": filename,
                "file_size": os.path.getsize(filepath)
            }

        except Exception as e:
            logger.error(f"Error creating smart exam DOCX: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    def _get_field(self, exam_request: Union[SmartExamRequest, Dict[str, Any]], field: str, default: Any = None) -> Any:
        """Helper method to get field from either Pydantic model or dict"""
        if isinstance(exam_request, dict):
            return exam_request.get(field, default)
        else:
            return getattr(exam_request, field, default)

    def _setup_document_style(self, doc: Document):
        """Thiết lập style cho document"""
        try:
            # Thiết lập margins
            sections = doc.sections
            for section in sections:
                section.top_margin = Inches(0.8)
                section.bottom_margin = Inches(0.8)
                section.left_margin = Inches(0.8)
                section.right_margin = Inches(0.8)

            # Thiết lập font mặc định
            style = doc.styles['Normal']
            font = style.font
            font.name = 'Times New Roman'
            font.size = Pt(12)

        except Exception as e:
            logger.error(f"Error setting up document style: {e}")

    def _create_cover_page(self, doc: Document, exam_request: Union[SmartExamRequest, Dict[str, Any]], exam_data: Dict[str, Any]):
        """Tạo trang bìa theo chuẩn THPT 2025"""
        try:
            # Header với logo và thông tin trường
            header_table = doc.add_table(rows=1, cols=2)
            header_table.style = 'Table Grid'
            
            # Cột trái - Logo và thông tin bộ
            left_cell = header_table.cell(0, 0)
            left_para = left_cell.paragraphs[0]
            left_para.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER
            left_para.add_run("BỘ GIÁO DỤC VÀ ĐÀO TẠO").bold = True
            left_para.add_run("\n")
            left_para.add_run(self._get_field(exam_request, "school", "TRƯỜNG THPT ABC")).bold = True

            # Cột phải - Thông tin đề thi
            right_cell = header_table.cell(0, 1)
            right_para = right_cell.paragraphs[0]
            right_para.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER
            right_para.add_run("ĐỀ THAM KHẢO").bold = True
            right_para.add_run(f"\nMôn: {self._get_field(exam_request, 'subject', 'Hóa học')}")

            # Khoảng trống
            doc.add_paragraph()
            doc.add_paragraph()

            # Tiêu đề chính
            title_para = doc.add_paragraph()
            title_para.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER
            title_run = title_para.add_run(self._get_field(exam_request, "examTitle", "KIỂM TRA GIỮA KỲ"))
            title_run.bold = True
            title_run.font.size = Pt(16)

            # Thông tin chi tiết
            info_para = doc.add_paragraph()
            info_para.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER
            info_para.add_run(f"Môn: {self._get_field(exam_request, 'subject', 'Hóa học')} - Lớp {self._get_field(exam_request, 'grade', 12)}")
            info_para.add_run(f"\nThời gian làm bài: {self._get_field(exam_request, 'duration', 45)} phút")
            
            # Thống kê đề thi
            statistics = exam_data.get("statistics", {})
            total_questions = statistics.get("total_questions", 0)
            info_para.add_run(f"\n(Đề thi gồm {total_questions} câu)")

            # Ngắt trang
            doc.add_page_break()

        except Exception as e:
            logger.error(f"Error creating cover page: {e}")

    def _create_exam_info(self, doc: Document, exam_request: Union[SmartExamRequest, Dict[str, Any]], exam_data: Dict[str, Any]):
        """Tạo thông tin đề thi"""
        try:
            # Thông tin cơ bản
            info_para = doc.add_paragraph()
            info_para.add_run("Họ và tên thí sinh: ").bold = True
            info_para.add_run("." * 40)
            info_para.add_run("  Số báo danh: ").bold = True
            info_para.add_run("." * 20)

            doc.add_paragraph()

        except Exception as e:
            logger.error(f"Error creating exam info: {e}")

    def _create_chemistry_valence_table(self, doc: Document, questions: List[Dict[str, Any]]):
        """Tạo bảng hóa trị cho môn Hóa học"""
        try:
            # Tiêu đề
            valence_title = doc.add_paragraph()
            valence_title.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER
            valence_run = valence_title.add_run("BẢNG HÓA TRỊ CÁC NGUYÊN TỐ")
            valence_run.bold = True

            # Bảng hóa trị cơ bản
            valence_para = doc.add_paragraph()
            valence_para.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER
            valence_text = "H(I); Li, Na, K, Rb, Cs, Ag(I); Mg, Ca, Sr, Ba, Zn, Cd(II); Al(III); C, Si(IV); N(-III, III, V); P(-III, III, V); O(-II); S(-II, IV, VI); F(-I); Cl, Br, I(-I, I, III, V, VII); Fe(II, III); Cu(I, II); Pb(II, IV); Sn(II, IV); Mn(II, IV, VI, VII); Cr(II, III, VI)"
            valence_para.add_run(valence_text)

            doc.add_paragraph()

        except Exception as e:
            logger.error(f"Error creating chemistry valence table: {e}")

    def _create_exam_content_by_parts(self, doc: Document, questions: List[Dict[str, Any]]):
        """Tạo nội dung đề thi theo 3 phần chuẩn THPT 2025"""
        try:
            print(f"DEBUG DOCX: Total questions received: {len(questions)}")
            for i, q in enumerate(questions[:3]):  # Show first 3 questions
                print(f"DEBUG DOCX: Question {i+1} keys: {list(q.keys())}")
                print(f"DEBUG DOCX: Question {i+1} part: {q.get('part', 'NO_PART')}")

            # Phân loại câu hỏi theo phần
            part_1_questions = [q for q in questions if q.get("part") == 1]
            part_2_questions = [q for q in questions if q.get("part") == 2]
            part_3_questions = [q for q in questions if q.get("part") == 3]

            print(f"DEBUG DOCX: Part 1 questions: {len(part_1_questions)}")
            print(f"DEBUG DOCX: Part 2 questions: {len(part_2_questions)}")
            print(f"DEBUG DOCX: Part 3 questions: {len(part_3_questions)}")

            # PHẦN I: Câu trắc nghiệm nhiều phương án lựa chọn
            if part_1_questions:
                self._create_part_1_section(doc, part_1_questions)

            # PHẦN II: Câu trắc nghiệm đúng sai
            if part_2_questions:
                self._create_part_2_section(doc, part_2_questions)

            # PHẦN III: Câu trắc nghiệm trả lời ngắn
            if part_3_questions:
                self._create_part_3_section(doc, part_3_questions)

        except Exception as e:
            logger.error(f"Error creating exam content by parts: {e}")

    def _create_part_1_section(self, doc: Document, questions: List[Dict[str, Any]]):
        """Tạo PHẦN I: Câu trắc nghiệm nhiều phương án lựa chọn"""
        try:
            # Tiêu đề phần
            part_title = doc.add_paragraph()
            part_title_run = part_title.add_run("PHẦN I. Câu trắc nghiệm nhiều phương án lựa chọn. ")
            part_title_run.bold = True
            part_title.add_run(f"Thí sinh trả lời từ câu 1 đến câu {len(questions)}.")

            note_para = doc.add_paragraph()
            note_para.add_run("(Mỗi câu trả lời đúng thí sinh được 0,25 điểm)")

            doc.add_paragraph()

            # Tạo câu hỏi
            for i, question in enumerate(questions, 1):
                self._create_multiple_choice_question(doc, question, i)

        except Exception as e:
            logger.error(f"Error creating part 1 section: {e}")

    def _create_part_2_section(self, doc: Document, questions: List[Dict[str, Any]]):
        """Tạo PHẦN II: Câu trắc nghiệm đúng sai"""
        try:
            doc.add_paragraph()
            
            # Tiêu đề phần
            part_title = doc.add_paragraph()
            part_title_run = part_title.add_run("PHẦN II. Câu trắc nghiệm đúng sai. ")
            part_title_run.bold = True
            part_title.add_run(f"Thí sinh trả lời từ câu 1 đến câu {len(questions)}. Trong mỗi ý a), b), c), d) ở mỗi câu, thí sinh chọn đúng hoặc sai.")

            # Hướng dẫn chấm điểm
            note_para = doc.add_paragraph()
            note_para.add_run("- Thí sinh chỉ lựa chọn chính xác 01 ý trong 01 câu hỏi được 0,1 điểm;")
            note_para.add_run("\n- Thí sinh chỉ lựa chọn chính xác 02 ý trong 01 câu hỏi được 0,25 điểm;")
            note_para.add_run("\n- Thí sinh chỉ lựa chọn chính xác 03 ý trong 01 câu hỏi được 0,5 điểm;")
            note_para.add_run("\n- Thí sinh lựa chọn chính xác cả 04 ý trong 01 câu hỏi được 1 điểm.")

            doc.add_paragraph()

            # Tạo câu hỏi
            for i, question in enumerate(questions, 1):
                self._create_true_false_question(doc, question, i)

        except Exception as e:
            logger.error(f"Error creating part 2 section: {e}")

    def _create_part_3_section(self, doc: Document, questions: List[Dict[str, Any]]):
        """Tạo PHẦN III: Câu trắc nghiệm trả lời ngắn"""
        try:
            doc.add_paragraph()
            
            # Tiêu đề phần
            part_title = doc.add_paragraph()
            part_title_run = part_title.add_run("PHẦN III. Câu trắc nghiệm trả lời ngắn. ")
            part_title_run.bold = True
            part_title.add_run(f"Thí sinh trả lời từ câu 1 đến câu {len(questions)}")

            note_para = doc.add_paragraph()
            note_para.add_run("Mỗi câu trả lời đúng thí sinh được 0,25 điểm.")

            doc.add_paragraph()

            # Tạo câu hỏi
            for i, question in enumerate(questions, 1):
                self._create_short_answer_question(doc, question, i)

        except Exception as e:
            logger.error(f"Error creating part 3 section: {e}")

    def _create_multiple_choice_question(self, doc: Document, question: Dict[str, Any], question_num: int):
        """Tạo câu hỏi trắc nghiệm nhiều phương án"""
        try:
            # Câu hỏi
            q_para = doc.add_paragraph()
            q_para.add_run(f"Câu {question_num}. ").bold = True
            q_para.add_run(question.get("cau_hoi", ""))

            # Các phương án
            dap_an = question.get("dap_an", {})
            for option in ["A", "B", "C", "D"]:
                if option in dap_an:
                    option_para = doc.add_paragraph()
                    option_para.add_run(f"{option}. {dap_an[option]}")

            doc.add_paragraph()

        except Exception as e:
            logger.error(f"Error creating multiple choice question: {e}")

    def _create_true_false_question(self, doc: Document, question: Dict[str, Any], question_num: int):
        """Tạo câu hỏi đúng sai"""
        try:
            # Câu hỏi chính
            q_para = doc.add_paragraph()
            q_para.add_run(f"Câu {question_num}. ").bold = True
            q_para.add_run(question.get("cau_hoi", ""))

            # Các ý a, b, c, d
            dap_an = question.get("dap_an", {})
            for option in ["a", "b", "c", "d"]:
                if option in dap_an:
                    option_para = doc.add_paragraph()
                    option_para.add_run(f"{option}) {dap_an[option]}")

            doc.add_paragraph()

        except Exception as e:
            logger.error(f"Error creating true false question: {e}")

    def _create_short_answer_question(self, doc: Document, question: Dict[str, Any], question_num: int):
        """Tạo câu hỏi trả lời ngắn"""
        try:
            # Câu hỏi
            q_para = doc.add_paragraph()
            q_para.add_run(f"Câu {question_num}. ").bold = True
            q_para.add_run(question.get("cau_hoi", ""))

            doc.add_paragraph()

        except Exception as e:
            logger.error(f"Error creating short answer question: {e}")

    def _add_exam_ending(self, doc: Document):
        """Thêm chữ 'Hết' vào cuối đề thi"""
        try:
            doc.add_paragraph()
            ending_para = doc.add_paragraph()
            ending_para.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER
            ending_run = ending_para.add_run("--- HẾT ---")
            ending_run.bold = True
            ending_run.font.size = Pt(14)

        except Exception as e:
            logger.error(f"Error adding exam ending: {e}")

    def _create_thpt_2025_answer_section(self, doc: Document, questions: List[Dict[str, Any]]):
        """Tạo phần đáp án theo chuẩn THPT 2025"""
        try:
            doc.add_paragraph()
            doc.add_paragraph()

            # Tiêu đề đáp án
            title_para = doc.add_paragraph()
            title_para.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER
            title_run = title_para.add_run("ĐÁP ÁN")
            title_run.bold = True
            title_run.font.size = Pt(14)

            doc.add_paragraph()

            # Phân loại câu hỏi theo phần
            part_1_questions = [q for q in questions if q.get("part") == 1]
            part_2_questions = [q for q in questions if q.get("part") == 2]
            part_3_questions = [q for q in questions if q.get("part") == 3]

            # Tạo đáp án cho từng phần
            if part_1_questions:
                self._create_part_1_answer_table(doc, part_1_questions)

            if part_2_questions:
                self._create_part_2_answer_table(doc, part_2_questions)

            if part_3_questions:
                self._create_part_3_answer_table(doc, part_3_questions)

        except Exception as e:
            logger.error(f"Error creating THPT 2025 answer section: {e}")

    def _create_part_1_answer_table(self, doc: Document, questions: List[Dict[str, Any]]):
        """Tạo bảng đáp án Phần I"""
        try:
            # Tiêu đề
            section_para = doc.add_paragraph()
            section_run = section_para.add_run("PHẦN I. Câu trắc nghiệm nhiều phương án lựa chọn. ")
            section_run.bold = True
            section_para.add_run(f"Thí sinh trả lời từ câu 1 đến câu {len(questions)}")

            note_para = doc.add_paragraph()
            note_para.add_run("(Mỗi câu trả lời đúng thí sinh được 0,25 điểm)")

            # Tạo bảng đáp án
            cols_per_row = 10
            questions_per_row = 9
            num_rows = (len(questions) + questions_per_row - 1) // questions_per_row

            table = doc.add_table(rows=num_rows * 2, cols=cols_per_row)
            table.style = 'Table Grid'

            # Điền dữ liệu
            for row_idx in range(num_rows):
                header_row = row_idx * 2
                answer_row = row_idx * 2 + 1

                table.cell(header_row, 0).text = "Câu"
                table.cell(answer_row, 0).text = "Chọn"

                for col_idx in range(1, cols_per_row):
                    question_idx = row_idx * questions_per_row + col_idx - 1
                    if question_idx < len(questions):
                        table.cell(header_row, col_idx).text = str(question_idx + 1)
                        
                        # Lấy đáp án đúng
                        dap_an = questions[question_idx].get("dap_an", {})
                        correct_answer = dap_an.get("dung", "A")
                        table.cell(answer_row, col_idx).text = correct_answer

        except Exception as e:
            logger.error(f"Error creating part 1 answer table: {e}")

    def _create_part_2_answer_table(self, doc: Document, questions: List[Dict[str, Any]]):
        """Tạo bảng đáp án Phần II"""
        try:
            doc.add_paragraph()
            
            section_para = doc.add_paragraph()
            section_run = section_para.add_run("PHẦN II. Câu trắc nghiệm đúng sai. ")
            section_run.bold = True
            section_para.add_run(f"Thí sinh trả lời từ câu 1 đến câu {len(questions)}.")

            # Tạo bảng đáp án
            table = doc.add_table(rows=len(questions) + 1, cols=5)
            table.style = 'Table Grid'

            # Header
            table.cell(0, 0).text = "Câu"
            table.cell(0, 1).text = "1"
            table.cell(0, 2).text = "2"
            table.cell(0, 3).text = "3"
            table.cell(0, 4).text = "4"

            # Đáp án
            for i, question in enumerate(questions):
                table.cell(i + 1, 0).text = "Đáp án"
                dap_an = question.get("dap_an", {})
                
                for j, option in enumerate(["a", "b", "c", "d"], 1):
                    if j <= 4:
                        answer = dap_an.get(option, "Đúng")
                        table.cell(i + 1, j).text = f"{option}) {answer}"

        except Exception as e:
            logger.error(f"Error creating part 2 answer table: {e}")

    def _create_part_3_answer_table(self, doc: Document, questions: List[Dict[str, Any]]):
        """Tạo bảng đáp án Phần III"""
        try:
            doc.add_paragraph()
            
            section_para = doc.add_paragraph()
            section_run = section_para.add_run("PHẦN III. Câu trắc nghiệm trả lời ngắn. ")
            section_run.bold = True
            section_para.add_run(f"Thí sinh trả lời từ câu 1 đến câu {len(questions)}")

            note_para = doc.add_paragraph()
            note_para.add_run("Mỗi câu trả lời đúng thí sinh được 0,25 điểm.")

            # Tạo bảng đáp án
            table = doc.add_table(rows=2, cols=len(questions) + 1)
            table.style = 'Table Grid'

            # Header
            table.cell(0, 0).text = "Câu"
            table.cell(1, 0).text = "Đáp án"

            # Đáp án
            for i, question in enumerate(questions):
                table.cell(0, i + 1).text = str(i + 1)
                dap_an = question.get("dap_an", {})
                answer = dap_an.get("dap_an", "")
                table.cell(1, i + 1).text = str(answer)

        except Exception as e:
            logger.error(f"Error creating part 3 answer table: {e}")

    def _generate_filename(self, exam_request: Union[SmartExamRequest, Dict[str, Any]]) -> str:
        """Tạo tên file"""
        try:
            subject = self._get_field(exam_request, "subject", "exam")
            grade = self._get_field(exam_request, "grade", "")
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            # Làm sạch tên file
            safe_subject = "".join(c for c in subject if c.isalnum() or c in (' ', '-', '_')).rstrip()
            
            return f"smart_exam_{safe_subject}_lop{grade}_{timestamp}.docx"
            
        except Exception as e:
            logger.error(f"Error generating filename: {e}")
            return f"smart_exam_{datetime.now().strftime('%Y%m%d_%H%M%S')}.docx"


# Singleton instance
smart_exam_docx_service = SmartExamDocxService()
