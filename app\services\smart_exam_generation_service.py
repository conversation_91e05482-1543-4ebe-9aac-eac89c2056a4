"""
Service cho việc tạo đề thi thông minh theo chuẩn THPT 2025
"""

import logging
import json
import asyncio
from typing import Dict, Any, List, Optional
from datetime import datetime

from app.models.smart_exam_models import SmartExamRequest, ExamStatistics
from app.services.openrouter_service import OpenRouterService
from app.core.config import settings

logger = logging.getLogger(__name__)


class SmartExamGenerationService:
    """Service tạo đề thi thông minh theo chuẩn THPT 2025"""

    def __init__(self):
        self.llm_service = OpenRouterService()

    async def generate_smart_exam(
        self, exam_request: SmartExamRequest, lesson_content: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Tạo đề thi thông minh theo chuẩn THPT 2025

        Args:
            exam_request: Request chứa ma trận đề thi
            lesson_content: Nội dung bài học từ Qdrant

        Returns:
            Dict chứa đề thi đã được tạo
        """
        try:
            start_time = datetime.now()
            logger.info(f"Starting smart exam generation for subject: {exam_request.subject}")

            if not self.llm_service.is_available():
                return {
                    "success": False,
                    "error": "LLM service not available. Please check OpenRouter API configuration."
                }

            # Tạo câu hỏi cho từng phần theo chuẩn THPT 2025
            all_questions = []
            part_statistics = {"part_1": 0, "part_2": 0, "part_3": 0}

            for lesson_matrix in exam_request.matrix:
                lesson_questions = await self._generate_questions_for_lesson(
                    lesson_matrix, lesson_content, exam_request.subject
                )
                
                # Phân loại câu hỏi theo phần
                for question in lesson_questions:
                    part_num = question.get("part", 1)
                    part_statistics[f"part_{part_num}"] += 1
                
                all_questions.extend(lesson_questions)

            # Sắp xếp câu hỏi theo phần và đánh số lại
            sorted_questions = self._sort_and_renumber_questions(all_questions)

            # Tính toán thống kê
            end_time = datetime.now()
            generation_time = (end_time - start_time).total_seconds()
            
            statistics = self._calculate_statistics(
                sorted_questions, exam_request, generation_time
            )

            return {
                "success": True,
                "exam_id": f"smart_exam_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                "questions": sorted_questions,
                "statistics": statistics,
                "total_generated": len(sorted_questions),
                "exam_request": exam_request.model_dump()
            }

        except Exception as e:
            logger.error(f"Error generating smart exam: {e}")
            return {
                "success": False,
                "error": f"Failed to generate smart exam: {str(e)}"
            }

    async def _generate_questions_for_lesson(
        self, lesson_matrix, lesson_content: Dict[str, Any], subject: str
    ) -> List[Dict[str, Any]]:
        """Tạo câu hỏi cho một bài học theo ma trận"""
        try:
            lesson_id = lesson_matrix.lessonId
            logger.info(f"Generating questions for lesson: {lesson_id}")

            # Lấy nội dung bài học
            lesson_data = lesson_content.get(lesson_id, {})
            if not lesson_data:
                logger.warning(f"No content found for lesson: {lesson_id}")
                return []

            all_lesson_questions = []

            # Tạo câu hỏi cho từng phần
            for part in lesson_matrix.parts:
                part_questions = await self._generate_questions_for_part(
                    part, lesson_data, subject, lesson_id
                )
                all_lesson_questions.extend(part_questions)

            return all_lesson_questions

        except Exception as e:
            logger.error(f"Error generating questions for lesson {lesson_matrix.lessonId}: {e}")
            return []

    async def _generate_questions_for_part(
        self, part, lesson_data: Dict[str, Any], subject: str, lesson_id: str
    ) -> List[Dict[str, Any]]:
        """Tạo câu hỏi cho một phần cụ thể"""
        try:
            part_questions = []
            part_num = part.part
            objectives = part.objectives

            # Tạo câu hỏi cho từng mức độ nhận thức
            for level, count in [("Biết", objectives.Biết), ("Hiểu", objectives.Hiểu), ("Vận_dụng", objectives.Vận_dụng)]:
                if count > 0:
                    level_questions = await self._generate_questions_for_level(
                        part_num, level, count, lesson_data, subject, lesson_id
                    )
                    part_questions.extend(level_questions)

            return part_questions

        except Exception as e:
            logger.error(f"Error generating questions for part {part.part}: {e}")
            return []

    async def _generate_questions_for_level(
        self, part_num: int, level: str, count: int, lesson_data: Dict[str, Any], 
        subject: str, lesson_id: str
    ) -> List[Dict[str, Any]]:
        """Tạo câu hỏi cho một mức độ nhận thức cụ thể"""
        try:
            # Xác định loại câu hỏi theo phần
            question_type = self._get_question_type_by_part(part_num)
            
            # Tạo prompt cho LLM
            prompt = self._create_prompt_for_level(
                part_num, level, count, question_type, lesson_data, subject, lesson_id
            )

            # Gọi LLM để tạo câu hỏi
            response = await self.llm_service.generate_content(
                prompt=prompt,
                temperature=0.3,
                max_tokens=4000
            )

            # Debug logging for LLM response
            logger.info(f"DEBUG: LLM response success: {response.get('success', False)}")
            if response.get("success", False):
                response_text = response.get("text", "")
                logger.info(f"DEBUG: LLM response length: {len(response_text)}")
                logger.info(f"DEBUG: LLM response preview: {response_text[:300]}...")
            else:
                logger.error(f"DEBUG: LLM error: {response.get('error', 'Unknown error')}")

            if not response.get("success", False):
                logger.error(f"LLM failed for part {part_num}, level {level}: {response.get('error')}")
                return []

            # Parse response JSON
            questions = self._parse_llm_response(response.get("text", ""), part_num, level, lesson_id)

            # Debug logging for parsed questions
            logger.info(f"DEBUG: Parsed {len(questions)} questions from LLM response")
            
            # Giới hạn số câu hỏi theo yêu cầu
            return questions[:count]

        except Exception as e:
            logger.error(f"Error generating questions for level {level}: {e}")
            return []

    def _get_question_type_by_part(self, part_num: int) -> str:
        """Xác định loại câu hỏi theo phần"""
        if part_num == 1:
            return "TN"  # Trắc nghiệm nhiều phương án
        elif part_num == 2:
            return "DS"  # Đúng/Sai
        elif part_num == 3:
            return "DT"  # Điền từ/Trả lời ngắn
        else:
            return "TN"  # Default

    def _create_prompt_for_level(
        self, part_num: int, level: str, count: int, question_type: str,
        lesson_data: Dict[str, Any], subject: str, lesson_id: str
    ) -> str:
        """Create prompt for LLM according to THPT 2025 standards"""

        # Get lesson content - fix the data structure access
        lesson_content_data = lesson_data.get("content", {})
        main_content = lesson_content_data.get("main_content", "")
        lesson_info = lesson_content_data.get("lesson_info", {})

        # Debug logging
        logger.info(f"DEBUG: lesson_data keys: {list(lesson_data.keys()) if isinstance(lesson_data, dict) else 'Not a dict'}")
        logger.info(f"DEBUG: lesson_content_data keys: {list(lesson_content_data.keys()) if isinstance(lesson_content_data, dict) else 'Not a dict'}")
        logger.info(f"DEBUG: main_content type: {type(main_content)}")
        logger.info(f"DEBUG: main_content value: {str(main_content)[:100]}...")

        # Ensure main_content is string and limit length
        if isinstance(main_content, str):
            content_preview = main_content[:2000] if len(main_content) > 2000 else main_content
        elif isinstance(main_content, list):
            # If it's a list, join the items
            content_preview = " ".join(str(item) for item in main_content)[:2000]
        else:
            content_preview = str(main_content)[:2000] if main_content else ""

        # Part descriptions
        part_descriptions = {
            1: "PART I: Multiple choice questions (A, B, C, D)",
            2: "PART II: True/False questions (each question has 4 statements a, b, c, d)",
            3: "PART III: Short answer questions (fill in numbers or short words)"
        }

        prompt = f"""
You are an expert in creating {subject} exams according to THPT 2025 standards.

{part_descriptions.get(part_num, "")}

LESSON INFORMATION:
- Lesson: {lesson_id}
- Chapter: {lesson_info.get('chapter_title', '')}
- Content: {content_preview}...

REQUIREMENTS:
- Create {count} questions at "{level}" cognitive level
- Part {part_num} - {self._get_part_description(part_num)}
- Questions must be based on lesson content
- Ensure scientific accuracy

{self._get_specific_instructions_by_part(part_num, level)}

JSON RESPONSE FORMAT:
[
    {{
        "question": "Question content",
        "answer": {self._get_answer_format_by_part(part_num)},
        "explanation": "Answer explanation",
        "cognitive_level": "{level}",
        "part": {part_num}
    }}
]

Return only JSON, no additional text.
"""
        return prompt

    def _get_part_description(self, part_num: int) -> str:
        """Get detailed description for each part"""
        descriptions = {
            1: "Multiple choice questions",
            2: "True/False questions",
            3: "Short answer questions"
        }
        return descriptions.get(part_num, "")

    def _get_specific_instructions_by_part(self, part_num: int, level: str) -> str:
        """Hướng dẫn cụ thể cho từng phần"""
        if part_num == 1:
            return """
HƯỚNG DẪN PHẦN I:
- Mỗi câu có 4 phương án A, B, C, D
- Chỉ có 1 đáp án đúng
- Câu hỏi rõ ràng, không gây nhầm lẫn
"""
        elif part_num == 2:
            return """
HƯỚNG DẪN PHẦN II:
- Mỗi câu có 4 ý a), b), c), d)
- Mỗi ý có thể Đúng hoặc Sai
- Thí sinh chọn Đúng/Sai cho từng ý
"""
        elif part_num == 3:
            return """
HƯỚNG DẪN PHẦN III:
- Câu hỏi yêu cầu điền số hoặc từ ngắn
- Đáp án là giá trị cụ thể
- Thường là kết quả tính toán hoặc khái niệm
"""
        return ""

    def _get_answer_format_by_part(self, part_num: int) -> str:
        """Format đáp án theo từng phần"""
        if part_num == 1:
            return '{"A": "Phương án A", "B": "Phương án B", "C": "Phương án C", "D": "Phương án D", "dung": "A"}'
        elif part_num == 2:
            return '{"a": "Đúng", "b": "Sai", "c": "Đúng", "d": "Sai"}'
        elif part_num == 3:
            return '{"dap_an": "Giá trị đáp án"}'
        return '{"dung": "A"}'

    def _parse_llm_response(self, response_text: str, part_num: int, level: str, lesson_id: str) -> List[Dict[str, Any]]:
        """Parse response từ LLM"""
        try:
            # Tìm JSON trong response
            start_idx = response_text.find('[')
            end_idx = response_text.rfind(']') + 1
            
            if start_idx == -1 or end_idx == 0:
                logger.error("No JSON array found in LLM response")
                return []

            json_str = response_text[start_idx:end_idx]
            questions = json.loads(json_str)

            # Validate và bổ sung thông tin
            validated_questions = []
            for q in questions:
                if isinstance(q, dict) and "question" in q:
                    q["part"] = part_num
                    q["cognitive_level"] = level
                    q["lesson_id"] = lesson_id
                    q["question_type"] = self._get_question_type_by_part(part_num)
                    validated_questions.append(q)

            logger.info(f"DEBUG: Validated {len(validated_questions)} questions out of {len(questions)} raw questions")
            return validated_questions

        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse JSON from LLM response: {e}")
            return []
        except Exception as e:
            logger.error(f"Error parsing LLM response: {e}")
            return []

    def _sort_and_renumber_questions(self, questions: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Sắp xếp câu hỏi theo phần và đánh số lại"""
        try:
            # Sắp xếp theo phần
            sorted_questions = sorted(questions, key=lambda x: x.get("part", 1))
            
            # Đánh số lại theo từng phần
            part_counters = {1: 1, 2: 1, 3: 1}
            
            for question in sorted_questions:
                part = question.get("part", 1)
                question["stt"] = part_counters[part]
                question["stt_global"] = len([q for q in sorted_questions[:sorted_questions.index(question)+1]])
                part_counters[part] += 1

            return sorted_questions

        except Exception as e:
            logger.error(f"Error sorting and renumbering questions: {e}")
            return questions

    def _calculate_statistics(
        self, questions: List[Dict[str, Any]], exam_request: SmartExamRequest, generation_time: float
    ) -> ExamStatistics:
        """Tính toán thống kê đề thi"""
        try:
            # Đếm câu hỏi theo phần
            part_counts = {1: 0, 2: 0, 3: 0}
            difficulty_counts = {"Biết": 0, "Hiểu": 0, "Vận_dụng": 0}
            
            for question in questions:
                part = question.get("part", 1)
                part_counts[part] += 1
                
                level = question.get("muc_do", "Biết")
                if level in difficulty_counts:
                    difficulty_counts[level] += 1

            return ExamStatistics(
                total_questions=len(questions),
                part_1_questions=part_counts[1],
                part_2_questions=part_counts[2],
                part_3_questions=part_counts[3],
                lessons_used=len(exam_request.matrix),
                difficulty_distribution=difficulty_counts,
                generation_time=generation_time,
                created_at=datetime.now().isoformat()
            )

        except Exception as e:
            logger.error(f"Error calculating statistics: {e}")
            return ExamStatistics(
                total_questions=len(questions),
                part_1_questions=0,
                part_2_questions=0,
                part_3_questions=0,
                lessons_used=0,
                difficulty_distribution={"Biết": 0, "Hiểu": 0, "Vận_dụng": 0},
                generation_time=generation_time,
                created_at=datetime.now().isoformat()
            )


# Singleton instance
smart_exam_generation_service = SmartExamGenerationService()
